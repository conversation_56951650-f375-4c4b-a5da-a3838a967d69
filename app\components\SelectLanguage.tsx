"use client";

import { useState, useEffect } from "react";
import { useTranslations, type Language } from "../translations";

interface LanguageOption {
  name: string;
  flag: string;
}

interface SelectLanguageProps {
  variant?: "desktop" | "mobile";
  onLanguageChange?: (language: string) => void;
  onMobileMenuClose?: () => void;
}

// Global state to share language across all instances
let globalLanguage: string | null = null;
let globalIsLoaded = false;
const languageListeners: Set<(lang: string) => void> = new Set();

// Function to notify all instances of language change
const notifyLanguageChange = (newLanguage: string) => {
  globalLanguage = newLanguage;
  // Use setTimeout to ensure all components update in the next tick
  setTimeout(() => {
    languageListeners.forEach((listener) => listener(newLanguage));
  }, 0);
};

export default function SelectLanguage({
  variant = "desktop",
  onLanguageChange,
  onMobileMenuClose,
}: SelectLanguageProps) {
  const [isLangMenuOpen, setIsLangMenuOpen] = useState(false);
  const [currentLanguage, setCurrentLanguage] = useState<Language | null>(
    globalLanguage as Language
  );
  const [isLoaded, setIsLoaded] = useState(globalIsLoaded);
  const [forceUpdate, setForceUpdate] = useState(0);

  const languages: Record<Language, LanguageOption> = {
    en: { name: "English", flag: "🇺🇸" },
    ar: { name: "العربية", flag: "🇸🇦" },
  };

  // Get translations for current language
  const t = useTranslations(currentLanguage || "en");

  // Apply RTL direction when Arabic is selected
  useEffect(() => {
    if (currentLanguage === "ar") {
      document.documentElement.dir = "rtl";
      document.documentElement.lang = "ar";
    } else {
      document.documentElement.dir = "ltr";
      document.documentElement.lang = "en";
    }
  }, [currentLanguage]);

  // Load language from localStorage on component mount
  useEffect(() => {
    if (!globalIsLoaded) {
      const savedLanguage = localStorage.getItem("language") as Language;
      const lang = savedLanguage || "en";
      globalLanguage = lang;
      globalIsLoaded = true;
      setCurrentLanguage(lang as Language);
      setIsLoaded(true);
      notifyLanguageChange(lang);
    } else {
      setCurrentLanguage(globalLanguage as Language);
      setIsLoaded(true);
    }

    // Listen for language changes from other instances
    const handleGlobalLanguageChange = (newLanguage: string) => {
      setCurrentLanguage(newLanguage as Language);
      // Force re-render by updating forceUpdate counter
      setForceUpdate((prev) => prev + 1);
    };

    languageListeners.add(handleGlobalLanguageChange);

    return () => {
      languageListeners.delete(handleGlobalLanguageChange);
    };
  }, []);

  // Additional effect to sync with global state changes
  useEffect(() => {
    if (globalLanguage && globalLanguage !== currentLanguage) {
      setCurrentLanguage(globalLanguage as Language);
      setForceUpdate((prev) => prev + 1);
    }
  }, [currentLanguage]);

  // Effect to ensure UI updates when forceUpdate changes
  useEffect(() => {
    // This effect runs whenever forceUpdate changes, ensuring re-render
  }, [forceUpdate]);

  // Close language dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest(".language-dropdown")) {
        setIsLangMenuOpen(false);
      }
    };

    if (isLangMenuOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isLangMenuOpen]);

  // Save language to localStorage when it changes
  const handleLanguageChange = (lang: string) => {
    // Update local state immediately
    setCurrentLanguage(lang as Language);
    localStorage.setItem("language", lang);
    setIsLangMenuOpen(false);
    onLanguageChange?.(lang);
    if (variant === "mobile") {
      onMobileMenuClose?.();
    }
    // Notify all instances of the change
    notifyLanguageChange(lang);

    // Dispatch custom event for other components to listen
    window.dispatchEvent(new CustomEvent("languageChange", { detail: lang }));

    // Force re-render to ensure UI updates
    setForceUpdate((prev) => prev + 1);
  };

  if (variant === "mobile") {
    return (
      <>
        {isLoaded && (
          <div
            key={`mobile-${currentLanguage}-${forceUpdate}`}
            className="pt-3 border-t border-gray-200"
          >
            <div className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3 px-4">
              {t.language.title}
            </div>
            <div className="space-y-2">
              {Object.entries(languages).map(([code, lang]) => (
                <button
                  key={code}
                  onClick={() => handleLanguageChange(code)}
                  className={`w-full text-left px-4 py-3 rounded-xl text-base font-medium transition-all duration-300 flex items-center space-x-3 cursor-pointer ${
                    currentLanguage === code
                      ? "bg-gradient-to-r from-blue-50 to-indigo-50 text-blue-700 border border-blue-200"
                      : "text-gray-700 hover:text-blue-600 hover:bg-blue-50 border border-transparent hover:border-blue-200"
                  }`}
                >
                  <span className="text-xl">{lang.flag}</span>
                  <span className="flex-1">{lang.name}</span>
                  {currentLanguage === code && (
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  )}
                </button>
              ))}
            </div>
          </div>
        )}
      </>
    );
  }

  // Desktop variant
  return (
    <div
      key={`desktop-${currentLanguage}-${forceUpdate}`}
      className="relative language-dropdown"
    >
      <button
        onClick={() => setIsLangMenuOpen(!isLangMenuOpen)}
        className="group flex items-center space-x-2 bg-gradient-to-r from-gray-50 to-gray-100 hover:from-blue-50 hover:to-blue-100 border border-gray-200 hover:border-blue-300 px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 shadow-sm hover:shadow-md cursor-pointer"
      >
        <div className="flex items-center space-x-2">
          {isLoaded && currentLanguage ? (
            <>
              <span className="text-lg">
                {languages[currentLanguage as keyof typeof languages].flag}
              </span>
              <span className="text-gray-700 group-hover:text-blue-700 font-medium">
                {languages[currentLanguage as keyof typeof languages].name}
              </span>
            </>
          ) : (
            <>
              <div className="w-5 h-5 bg-gray-200 rounded animate-pulse"></div>
              <div className="w-16 h-4 bg-gray-200 rounded animate-pulse"></div>
            </>
          )}
        </div>
        <svg
          className={`w-4 h-4 text-gray-500 group-hover:text-blue-600 transition-all duration-300 ${
            isLangMenuOpen ? "rotate-180" : ""
          }`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </button>

      {/* Language Dropdown Menu */}
      {isLoaded && (
        <div
          className={`absolute right-0 mt-3 w-56 bg-white rounded-2xl shadow-2xl border border-gray-100 z-50 overflow-hidden transition-all duration-300 transform ${
            isLangMenuOpen
              ? "opacity-100 scale-100 translate-y-0"
              : "opacity-0 scale-95 -translate-y-2 pointer-events-none"
          }`}
        >
          <div className="py-2 pb-0">
            <div className="px-4 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider border-b border-gray-100">
              {t.language.selectLanguage}
            </div>
            {Object.entries(languages).map(([code, lang]) => (
              <button
                key={code}
                onClick={() => handleLanguageChange(code)}
                className={`w-full text-left px-4 py-3 text-sm hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 flex items-center space-x-3 transition-all duration-200 group cursor-pointer ${
                  currentLanguage === code
                    ? "bg-gradient-to-r from-blue-50 to-indigo-50 text-blue-700 border-r-4 border-blue-500"
                    : "text-gray-700 hover:text-blue-700"
                }`}
              >
                <span className="text-xl">{lang.flag}</span>
                <span className="font-medium flex-1">{lang.name}</span>
                {currentLanguage === code && (
                  <div className="flex items-center">
                    <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                    <svg
                      className="w-4 h-4 ml-2 text-blue-600"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                )}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
